package com.dateup.android.subscriptions.views

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.dateup.android.ui.BaseEdgeToEdgeActivity
import com.dateup.android.R
import com.dateup.android.activity.GuestHeightPreferenceActivity
import com.dateup.android.activity.GuestMatchMakingActivity
import com.dateup.android.activity.Images3Activity
import com.dateup.android.databinding.ActivityDateUpPledgeBinding
import com.dateup.android.databinding.ActivityDateUpPlusCelebrationBinding
import com.dateup.android.databinding.AgeRestrictionActivityBinding
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.models.GenderType
import com.dateup.android.models.InterestedInGender
import com.dateup.android.models.UserObject
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Utils
import nl.dionsegijn.konfetti.models.Shape
import nl.dionsegijn.konfetti.models.Size

class DateUpPlusCelebrationActivity : BaseEdgeToEdgeActivity() {

    private lateinit var binding: ActivityDateUpPlusCelebrationBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDateUpPlusCelebrationBinding.inflate(layoutInflater)
        this.setContentView(binding.root)

        binding.buttonLargeActiveButton.setOnClickListener {
            startProfileActivity()
        }

        setupSubscriptionContent()
        startKonfetti()
    }

    private fun startKonfetti() {
        binding.viewKonfetti.build()
            .addColors(resources.getColor(R.color.grey4, null), resources.getColor(R.color.konfetti_color2, null))
            .setDirection(0.0, 359.0)
            .setSpeed(1f, 5f)
            .setFadeOutEnabled(true)
            .setTimeToLive(2000L)
            .addShapes(Shape.Square, Shape.Circle)
            .addSizes(Size(12))
            .setPosition(-50f, AppUtils.getScreenWidth(this) + 50f, -50f, -50f)
            .streamFor(300, 2000L)
    }

    private fun setupSubscriptionContent() {
        val isSelectUser = UserObject.isDateUpSelectUser == true

        if (isSelectUser) {
            setupSelectContent()
        } else {
            setupPlusContent()
        }
    }

    private fun setupSelectContent() {
        // Update welcome text and description for Select
        binding.congratsYouReAmtextView.text = getString(R.string.subscription_celebration_select_welcome_text)
        binding.menOver61AndWoTextView.text = getString(R.string.subscription_celebration_select_desc)

        // Setup Select features (5 points)
        binding.subscriptionPoint1Image.setImageResource(R.drawable.ic_welcome_subscription_select_point1)
        binding.subscriptionPoint1.text = getString(R.string.subscription_celebration_select_point_1)

        binding.subscriptionPoint2Image.setImageResource(R.drawable.ic_welcome_subscription_select_point2)
        binding.subscriptionPoint2.text = getString(R.string.subscription_celebration_select_point_2)

        binding.subscriptionPoint3Image.setImageResource(R.drawable.ic_welcome_subscription_select_point3)
        binding.subscriptionPoint3.text = getString(R.string.subscription_celebration_select_point_3)

        // Show additional points for Select
        binding.subscriptionPoint4Image.visibility = View.VISIBLE
        binding.subscriptionPoint4Image.setImageResource(R.drawable.ic_welcome_subscription_select_point4)
        binding.subscriptionPoint4.visibility = View.VISIBLE
        binding.subscriptionPoint4.text = getString(R.string.subscription_celebration_select_point_4)

        binding.subscriptionPoint5Image.visibility = View.VISIBLE
        binding.subscriptionPoint5Image.setImageResource(R.drawable.ic_welcome_subscription_select_point5)
        binding.subscriptionPoint5.visibility = View.VISIBLE
        binding.subscriptionPoint5.text = getString(R.string.subscription_celebration_select_point_5)
    }

    private fun setupPlusContent() {
        // Keep default Plus content (already set in layout)
        binding.congratsYouReAmtextView.text = getString(R.string.subscription_celebration_welcome_text)
        binding.menOver61AndWoTextView.text = getString(R.string.subscription_celebration_desc)

        // Setup Plus features (3 points) - using default layout values
        binding.subscriptionPoint1Image.setImageResource(R.drawable.ic_welcome_subscription_point1)
        binding.subscriptionPoint1.text = getString(R.string.subscription_celebration_point_1)

        binding.subscriptionPoint2Image.setImageResource(R.drawable.ic_welcome_subscription_point2)
        binding.subscriptionPoint2.text = getString(R.string.subscription_celebration_point_2)

        binding.subscriptionPoint3Image.setImageResource(R.drawable.ic_welcome_subscription_point3)
        binding.subscriptionPoint3.text = getString(R.string.subscription_celebration_point_3)

        // Hide additional points for Plus
        binding.subscriptionPoint4Image.visibility = View.GONE
        binding.subscriptionPoint4.visibility = View.GONE
        binding.subscriptionPoint5Image.visibility = View.GONE
        binding.subscriptionPoint5.visibility = View.GONE
    }

    private fun startProfileActivity() {
        val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
        this.startActivity(intent)
    }

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, DateUpPlusCelebrationActivity::class.java)
        }
    }
}
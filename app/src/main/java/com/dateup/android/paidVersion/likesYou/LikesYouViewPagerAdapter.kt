package com.dateup.android.paidVersion.likesYou

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter

class LikesYouViewPagerAdapter(
    activity: FragmentActivity,
    private val hasPlusOrSelect: <PERSON>olean,
    private val hasSelectSubscription: Boolean
) : FragmentStateAdapter(activity) {
    override fun getItemCount(): Int = 2

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> LikesYouFragment.newInstance(hasPlusOrSelect)
            1 -> PassedUsersFragment.newInstance(hasSelectSubscription)
            else -> throw IllegalStateException("Invalid tab")
        }
    }
}

package com.dateup.android.paidVersion.likesYou

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ProgressBar
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.AccountPreferences
import com.dateup.android.R
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.firebase.FirebaseGetAllLikedByUsersListListener
import com.dateup.android.firebase.FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore
import com.dateup.android.models.LikesTabUser
import com.dateup.android.utils.Constants
import com.dateup.android.models.UserObject
import com.dateup.android.subscriptions.views.SubscriptionActivity
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.utils.Utils
import com.dateup.android.viewModels.SpacesImagesViewModel
import com.google.firebase.firestore.DocumentSnapshot

class LikesYouFragment : Fragment() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var progressBar: ProgressBar
    private lateinit var blankFragment: View
    private lateinit var upgradeCTAButton: Button
    private lateinit var likesYouAdapter: LikesYouAdapter
    private lateinit var spacesImagesViewModel: SpacesImagesViewModel
    private var likesTabUsersList = arrayListOf<LikesTabUser>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_likes_you, container, false)
        recyclerView = view.findViewById(R.id.likes_you_recycler_view)
        progressBar = view.findViewById(R.id.progressBar)
        blankFragment = view.findViewById(R.id.blank_likes_you_fragment)
        upgradeCTAButton = view.findViewById(R.id.upgrade_cta_button)

        recyclerView.layoutManager = GridLayoutManager(requireContext(), 2)
        val spacing = resources.getDimensionPixelSize(R.dimen.likes_you_recycler_item_spacing)
        recyclerView.addItemDecoration(LikesYouItemDecoration(spacing))

        spacesImagesViewModel = ViewModelProvider(this)[SpacesImagesViewModel::class.java]

        likesYouAdapter = LikesYouAdapter(requireActivity(), spacesImagesViewModel)
        recyclerView.adapter = likesYouAdapter

        // Setup CTA button
        setupUpgradeCTA()

        getAllLikedUsers()
        return view
    }

    private fun getAllLikedUsers() {
        progressBar.visibility = View.VISIBLE
        val mainUserId = AccountPreferences.getInstance(requireContext()).getStringValue(Constants.firebaseUserId, "")
        FirebaseDatabaseUtil(requireContext()).getAllLikedByUsersList(object : FirebaseGetAllLikedByUsersListListener {
            override fun onSuccess(usersList: ArrayList<String>) {
                loadUsers(usersList)
            }

            override fun onFailure() {
                progressBar.visibility = View.GONE
            }
        }, mainUserId)
    }

    private fun loadUsers(userKeys: ArrayList<String>) {
        val likesTabUserList = arrayListOf<LikesTabUser>()
        if (userKeys.isEmpty()) {
            blankFragment.visibility = View.VISIBLE
            progressBar.visibility = View.GONE
            return
        }

        var loaded = 0
        for (userKey in userKeys) {
            FirebaseDatabaseUtil(requireContext())
                .readMainUserMapRawDataFromFirebaseFirestore(object : FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore {
                    override fun onSuccess(snapshot: DocumentSnapshot) {
                        val user = snapshot.toObject(LikesTabUser::class.java)
                        user?.likedYouUserFirebaseId = userKey
                        if (Utils.isValidLikesYouUser(user)) {
                            likesTabUserList.add(user!!)
                        }
                        if (++loaded == userKeys.size) {
                            onUsersLoaded(likesTabUserList)
                        }
                    }

                    override fun onFailure() {
                        if (++loaded == userKeys.size) {
                            onUsersLoaded(likesTabUserList)
                        }
                    }
                }, userKey)
        }
    }

    private fun setupUpgradeCTA() {
        // Show CTA button only for non-subscribers
        if (!UserObject.hasPlusOrSelect) {
            upgradeCTAButton.visibility = View.VISIBLE
            upgradeCTAButton.setOnClickListener {
                val subIntent = SubscriptionActivity.newIntent(requireContext())
                startActivity(subIntent.launchModeWithSingleTop())
            }
        } else {
            upgradeCTAButton.visibility = View.GONE
        }
    }

    private fun onUsersLoaded(users: List<LikesTabUser>) {
        progressBar.visibility = View.GONE
        if (users.isEmpty()) {
            blankFragment.visibility = View.VISIBLE
        } else {
            likesTabUsersList = ArrayList(users)
            likesYouAdapter.setLikesYouUsersList(likesTabUsersList)
        }
    }
}

package com.dateup.android.paidVersion.likesYou

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import androidx.viewpager2.widget.ViewPager2
import com.dateup.android.R
import com.dateup.android.models.UserObject
import com.dateup.android.ui.BottomNavigationBarActivity
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator

class LikesYouActivity : BottomNavigationBarActivity() {

    companion object {

        fun newIntent(context: Context): Intent {

            return Intent(context, LikesYouActivity::class.java)
        }
    }
    private lateinit var mLayoutContainer: FrameLayout


    override fun getBottomNavigationBarItem(): Int = R.id.navigation_likes_you

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        mLayoutContainer = findViewById(R.id.layout_container)

        val layout = View.inflate(this, R.layout.activity_likes_you, null)
        mLayoutContainer.addView(layout)

        val tabLayout = findViewById<TabLayout>(R.id.tab_layout)
        val viewPager = findViewById<ViewPager2>(R.id.view_pager)

        val adapter = LikesYouViewPagerAdapter(this, UserObject.hasPlusOrSelect, UserObject.isDateUpSelectUser == true)
        viewPager.adapter = adapter

        TabLayoutMediator(tabLayout, viewPager) { tab, position ->
            tab.text = when (position) {
                0 -> "Likes You"
                1 -> "Passed"
                else -> ""
            }
        }.attach()
    }
}



package com.dateup.android.paidVersion.likesYou

import android.app.Activity
import android.content.Intent
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.RenderEffect
import android.graphics.Shader
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.activity.OtherUserProfileModal
import com.dateup.android.databinding.ItemLikesYouBinding
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.glide.ImageLoaderModule
import com.dateup.android.models.LikesTabUser
import com.dateup.android.paidVersion.likesYou.LikesYouAdapter.Companion.EXTRA_LIKES_YOU_USER
import com.dateup.android.subscriptions.views.SubscriptionActivity
import com.dateup.android.models.UserObject
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils
import com.dateup.android.viewModels.SpacesImagesViewModel

class LikesYouAdapter(
    private val context: Activity,
    private val spacesImagesViewModel: SpacesImagesViewModel
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {

        const val EXTRA_LIKES_YOU_USER = "EXTRA_LIKES_YOU_USER"
        const val EXTRA_PASSED_USER = "EXTRA_PASSED_USER"
    }

    private var likesYourUsersList = arrayListOf<LikesTabUser>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {

        val itemBinding = ItemLikesYouBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        itemBinding.userProfilePhoto.clipToOutline = true
        return LikesYouUserViewHolder(itemBinding)
    }

    override fun getItemCount(): Int = likesYourUsersList.size

    override fun onBindViewHolder(viewHolder: RecyclerView.ViewHolder, position: Int) {

        val likesYouViewHolder = viewHolder as LikesYouUserViewHolder

        likesYouViewHolder.bindView(likesYourUsersList[position], context, spacesImagesViewModel)
    }

    fun setLikesYouUsersList(likesTabUsersMap: ArrayList<LikesTabUser>) {

        this.likesYourUsersList = likesTabUsersMap
        notifyDataSetChanged()
    }
}

class LikesYouUserViewHolder(private val itemBinding: ItemLikesYouBinding) : RecyclerView.ViewHolder(itemBinding.root) {

    fun bindView(likesTabUser: LikesTabUser, context: Activity, spacesImagesViewModel: SpacesImagesViewModel) {

        val userHeight = Utils.heightInFeetFromInchesWithQuotes(likesTabUser.height.toString().toDouble())

        val width = (AppUtils.getDpForImages(context, 156f))
        val height = AppUtils.getDpForImages(context, 156f)
        ImageLoaderModule.loadImageIntoImageViewNoCropWithLoadingAndCallback(context,spacesImagesViewModel,
                "${likesTabUser.likedYouUserFirebaseId}/${Constants.photoFileName1}",itemBinding.userProfilePhoto,
                shouldCacheImage = true, width = width, height = height)

        // Show/hide content based on subscription status
        if (!UserObject.hasPlusOrSelect) {
            applyPremiumLockedState(itemBinding, likesTabUser)
        } else {
            applyUnlockedState(itemBinding, likesTabUser, userHeight)
        }

        itemBinding.userProfilePhoto.setOnClickListener {
            if (UserObject.hasPlusOrSelect) {
                val intent = Intent(context, OtherUserProfileModal::class.java)
                intent.putExtra(EXTRA_LIKES_YOU_USER, likesTabUser)
                context.startActivity(intent)
            } else {
                // Show subscription activity for non-subscribers
                val subIntent = SubscriptionActivity.newIntent(context)
                context.startActivity(subIntent.launchModeWithSingleTop())
            }
        }
    }

    private fun applyPremiumLockedState(itemBinding: ItemLikesYouBinding, likesTabUser: LikesTabUser) {
        // Hide name and height, show placeholder
        itemBinding.userName.visibility = View.GONE
        itemBinding.userHeight.visibility = View.GONE
        itemBinding.lockedPlaceholder.visibility = View.VISIBLE
        itemBinding.lockIcon.visibility = View.VISIBLE

        // Apply premium frosted glass blur effect
        applyPremiumBlurEffect(itemBinding)
    }

    private fun applyUnlockedState(itemBinding: ItemLikesYouBinding, likesTabUser: LikesTabUser, userHeight: String) {
        // Show name and height, hide placeholder
        itemBinding.userName.text = likesTabUser.name.toString()
        itemBinding.userHeight.text = userHeight
        itemBinding.userName.visibility = View.VISIBLE
        itemBinding.userHeight.visibility = View.VISIBLE
        itemBinding.lockedPlaceholder.visibility = View.GONE
        itemBinding.lockIcon.visibility = View.GONE

        // Remove blur effect
        removePremiumBlurEffect(itemBinding)
    }

    private fun applyPremiumBlurEffect(itemBinding: ItemLikesYouBinding) {
        // Create frosted glass effect with progressive blur
        val colorMatrix = ColorMatrix()
        colorMatrix.setSaturation(0.2f) // Keep slight color for premium feel

        // Apply sophisticated darkening for premium locked appearance
        val premiumMatrix = ColorMatrix(floatArrayOf(
            0.8f, 0.0f, 0.0f, 0.0f, -15f,  // Red channel - preserve more detail
            0.0f, 0.8f, 0.0f, 0.0f, -15f,  // Green channel - preserve more detail
            0.0f, 0.0f, 0.8f, 0.0f, -15f,  // Blue channel - preserve more detail
            0.0f, 0.0f, 0.0f, 1.0f, 0.0f   // Alpha channel - keep as is
        ))

        colorMatrix.postConcat(premiumMatrix)
        val filter = ColorMatrixColorFilter(colorMatrix)
        itemBinding.userProfilePhoto.colorFilter = filter

        // Apply blur effect for Android 12+ (API 31+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val blurEffect = RenderEffect.createBlurEffect(8f, 8f, Shader.TileMode.CLAMP)
            itemBinding.userProfilePhoto.setRenderEffect(blurEffect)
        }

        // Reduce opacity for premium locked appearance
        itemBinding.userProfilePhoto.alpha = 0.2f
    }

    private fun removePremiumBlurEffect(itemBinding: ItemLikesYouBinding) {
        itemBinding.userProfilePhoto.colorFilter = null
        itemBinding.userProfilePhoto.alpha = 1.0f

        // Remove blur effect for Android 12+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            itemBinding.userProfilePhoto.setRenderEffect(null)
        }
    }
}
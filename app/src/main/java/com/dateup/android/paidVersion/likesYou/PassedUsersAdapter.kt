package com.dateup.android.paidVersion.likesYou

import android.app.Activity
import android.content.Intent
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.activity.OtherUserProfileModal
import com.dateup.android.databinding.ItemLikesYouBinding
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.glide.ImageLoaderModule
import com.dateup.android.models.LikesTabUser
import com.dateup.android.paidVersion.likesYou.LikesYouAdapter.Companion.EXTRA_PASSED_USER
import com.dateup.android.subscriptions.views.SubscriptionActivity
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils
import com.dateup.android.viewModels.SpacesImagesViewModel

class PassedUsersAdapter(
    private val context: Activity,
    private val spacesImagesViewModel: SpacesImagesViewModel,
    private val hasSelectSubscription: Boolean
) : RecyclerView.Adapter<PassedUsersAdapter.PassedUserViewHolder>() {

    private val users = arrayListOf<LikesTabUser>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PassedUserViewHolder {
        val itemBinding = ItemLikesYouBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        itemBinding.userProfilePhoto.clipToOutline = true
        return PassedUserViewHolder(itemBinding)
    }

    override fun getItemCount(): Int = users.size

    override fun onBindViewHolder(holder: PassedUserViewHolder, position: Int) {
        holder.bind(users[position], hasSelectSubscription)
    }

    fun appendUsers(newUsers: List<LikesTabUser>) {
        val start = users.size
        users.addAll(newUsers)
        notifyItemRangeInserted(start, newUsers.size)
    }

    fun setUsers(newUsers: List<LikesTabUser>) {
        users.clear()
        users.addAll(newUsers)
        notifyDataSetChanged()
    }

    inner class PassedUserViewHolder(private val binding: ItemLikesYouBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(user: LikesTabUser, hasSelectSubscription: Boolean) {

            val userHeight = Utils.heightInFeetFromInchesWithQuotes(user.height.toString().toDouble())

            binding.userName.text = user.name.toString()
            binding.userHeight.text = userHeight

            val width = AppUtils.getDpForImages(context, 156f)
            val height = AppUtils.getDpForImages(context, 156f)
            ImageLoaderModule.loadImageIntoImageViewNoCropWithLoadingAndCallback(
                context, spacesImagesViewModel,
                "${user.uid}/${Constants.photoFileName1}",
                binding.userProfilePhoto,
                shouldCacheImage = true, width = width, height = height
            )

            // Apply blur effect if user doesn't have Select subscription
            if (!hasSelectSubscription) {
                applyBlurEffect()
            } else {
                removeBlurEffect()
            }

            binding.userProfilePhoto.setOnClickListener {
                if (hasSelectSubscription) {
                    val intent = Intent(context, OtherUserProfileModal::class.java)
                    intent.putExtra(EXTRA_PASSED_USER, user)
                    context.startActivity(intent)
                } else {
                    // Show subscription activity for non-Select users
                    val subIntent = SubscriptionActivity.newIntent(context)
                    context.startActivity(subIntent.launchModeWithSingleTop())
                }
            }
        }

        private fun applyBlurEffect() {
            val colorMatrix = ColorMatrix()
            colorMatrix.setSaturation(0.3f) // Reduce saturation
            val filter = ColorMatrixColorFilter(colorMatrix)
            binding.userProfilePhoto.colorFilter = filter
            binding.userProfilePhoto.alpha = 0.6f // Make it more transparent
        }

        private fun removeBlurEffect() {
            binding.userProfilePhoto.colorFilter = null
            binding.userProfilePhoto.alpha = 1.0f
        }
    }
}

package com.dateup.android.paidVersion.likesYou

import android.app.Activity
import android.content.Intent
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.RenderEffect
import android.graphics.Shader
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.dateup.android.activity.OtherUserProfileModal
import com.dateup.android.databinding.ItemLikesYouBinding
import com.dateup.android.extensions.launchModeWithSingleTop
import com.dateup.android.glide.ImageLoaderModule
import com.dateup.android.models.LikesTabUser
import com.dateup.android.paidVersion.likesYou.LikesYouAdapter.Companion.EXTRA_PASSED_USER
import com.dateup.android.subscriptions.views.SubscriptionActivity
import com.dateup.android.models.UserObject
import com.dateup.android.utils.AppUtils
import com.dateup.android.utils.Constants
import com.dateup.android.utils.Utils
import com.dateup.android.viewModels.SpacesImagesViewModel

class PassedUsersAdapter(
    private val context: Activity,
    private val spacesImagesViewModel: SpacesImagesViewModel
) : RecyclerView.Adapter<PassedUsersAdapter.PassedUserViewHolder>() {

    private val users = arrayListOf<LikesTabUser>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PassedUserViewHolder {
        val itemBinding = ItemLikesYouBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        itemBinding.userProfilePhoto.clipToOutline = true
        return PassedUserViewHolder(itemBinding)
    }

    override fun getItemCount(): Int = users.size

    override fun onBindViewHolder(holder: PassedUserViewHolder, position: Int) {
        holder.bind(users[position])
    }

    fun appendUsers(newUsers: List<LikesTabUser>) {
        val start = users.size
        users.addAll(newUsers)
        notifyItemRangeInserted(start, newUsers.size)
    }

    fun setUsers(newUsers: List<LikesTabUser>) {
        users.clear()
        users.addAll(newUsers)
        notifyDataSetChanged()
    }

    inner class PassedUserViewHolder(private val binding: ItemLikesYouBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(user: LikesTabUser) {

            val userHeight = Utils.heightInFeetFromInchesWithQuotes(user.height.toString().toDouble())

            val width = AppUtils.getDpForImages(context, 156f)
            val height = AppUtils.getDpForImages(context, 156f)
            ImageLoaderModule.loadImageIntoImageViewNoCropWithLoadingAndCallback(
                context, spacesImagesViewModel,
                "${user.uid}/${Constants.photoFileName1}",
                binding.userProfilePhoto,
                shouldCacheImage = true, width = width, height = height
            )

            // Show/hide content based on subscription status
            if (UserObject.isDateUpSelectUser != true) {
                applyPremiumLockedState(user)
            } else {
                applyUnlockedState(user, userHeight)
            }

            binding.userProfilePhoto.setOnClickListener {
                if (UserObject.isDateUpSelectUser == true) {
                    val intent = Intent(context, OtherUserProfileModal::class.java)
                    intent.putExtra(EXTRA_PASSED_USER, user)
                    context.startActivity(intent)
                } else {
                    // Show subscription activity for non-Select users
                    val subIntent = SubscriptionActivity.newIntent(context)
                    context.startActivity(subIntent.launchModeWithSingleTop())
                }
            }
        }

        private fun applyPremiumLockedState(user: LikesTabUser) {
            // Hide name and height, show placeholder
            binding.userName.visibility = View.GONE
            binding.userHeight.visibility = View.GONE
            binding.lockedPlaceholder.visibility = View.VISIBLE
            binding.lockIcon.visibility = View.VISIBLE

            // Apply premium frosted glass blur effect
            applyPremiumBlurEffect()
        }

        private fun applyUnlockedState(user: LikesTabUser, userHeight: String) {
            // Show name and height, hide placeholder
            binding.userName.text = user.name.toString()
            binding.userHeight.text = userHeight
            binding.userName.visibility = View.VISIBLE
            binding.userHeight.visibility = View.VISIBLE
            binding.lockedPlaceholder.visibility = View.GONE
            binding.lockIcon.visibility = View.GONE

            // Remove blur effect
            removePremiumBlurEffect()
        }

        private fun applyPremiumBlurEffect() {
            // Create frosted glass effect with progressive blur
            val colorMatrix = ColorMatrix()
            colorMatrix.setSaturation(0.2f) // Keep slight color for premium feel

            // Apply sophisticated darkening for premium locked appearance
            val premiumMatrix = ColorMatrix(floatArrayOf(
                0.8f, 0.0f, 0.0f, 0.0f, -15f,  // Red channel - preserve more detail
                0.0f, 0.8f, 0.0f, 0.0f, -15f,  // Green channel - preserve more detail
                0.0f, 0.0f, 0.8f, 0.0f, -15f,  // Blue channel - preserve more detail
                0.0f, 0.0f, 0.0f, 1.0f, 0.0f   // Alpha channel - keep as is
            ))

            colorMatrix.postConcat(premiumMatrix)
            val filter = ColorMatrixColorFilter(colorMatrix)
            binding.userProfilePhoto.colorFilter = filter

            // Apply blur effect for Android 12+ (API 31+)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val blurEffect = RenderEffect.createBlurEffect(8f, 8f, Shader.TileMode.CLAMP)
                binding.userProfilePhoto.setRenderEffect(blurEffect)
            }

            // Reduce opacity for premium locked appearance
            binding.userProfilePhoto.alpha = 0.2f
        }

        private fun removePremiumBlurEffect() {
            binding.userProfilePhoto.colorFilter = null
            binding.userProfilePhoto.alpha = 1.0f

            // Remove blur effect for Android 12+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                binding.userProfilePhoto.setRenderEffect(null)
            }
        }
    }
}
